import { AxiosError } from "axios";
import FormData from "form-data";
import { logger } from "../../helpers/logger.js";
import { createPayrixApiClient } from "./api-client.js";

const apiClient = createPayrixApiClient();

export async function createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }> {
  try {
    const response = await apiClient.post("/notes", noteData);

    const noteResponse = response.data?.response?.data?.[0];
    if (!noteResponse?.id) {
      throw new Error("Invalid Payrix response structure: no note ID found");
    }

    return { id: noteResponse.id };
  } catch (error) {
    logger.error("Error creating note in Payrix", { error });
    if (error instanceof AxiosError) {
      logger.error("Payrix API error details", {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }
    throw error;
  }
}

export async function createNoteDocument(documentData: {
  note: string;
  file: { filename: string; content: Buffer; contentType: string };
  description?: string;
}): Promise<{ id: string }> {
  try {
    const noteDocumentId = await createNoteDocumentMetadata(documentData);
    await uploadFileToNoteDocument(noteDocumentId, documentData);

    return { id: noteDocumentId };
  } catch (error) {
    handleNoteDocumentError(error as AxiosError, documentData);
    throw error;
  }
}

async function createNoteDocumentMetadata(documentData: {
  note: string;
  file: { filename: string; content: Buffer; contentType: string };
  description?: string;
}): Promise<string> {
  const fileExtension = documentData.file.filename.split(".").pop()?.toLowerCase() || "png";

  const noteDocumentPayload = {
    note: documentData.note,
    type: fileExtension,
    documentType: "voidCheck",
    description: documentData.description || "Void check for bank account verification",
    name: documentData.file.filename,
  };

  const noteDocResponse = await apiClient.post("/noteDocuments", noteDocumentPayload);

  const noteDocumentId = noteDocResponse.data?.response?.data?.[0]?.id;
  if (!noteDocumentId) {
    throw new Error("Failed to create note document metadata - no ID returned");
  }

  return noteDocumentId;
}

async function uploadFileToNoteDocument(
  noteDocumentId: string,
  documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }
): Promise<void> {
  const formData = new FormData();

  formData.append("file", documentData.file.content, {
    filename: documentData.file.filename,
    contentType: documentData.file.contentType,
  });

  const uploadPayload = {
    // credential: PAYRIX_CREDENTIAL_ID || "default_credential",
    integration: "WORLDPAY",
    direction: "upload",
    name: documentData.file.filename,
    description: documentData.description || "Void Check for Bank Account Verification",
  };

  formData.append("json", JSON.stringify(uploadPayload));

  await apiClient.post(`/files/noteDocuments/${noteDocumentId}`, formData, {
    headers: {
      ...formData.getHeaders(),
    },
  });
}

function handleNoteDocumentError(
  error: AxiosError,
  documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }
): void {
  logger.error("Error in createNoteDocument 3-step process", {
    error,
    noteId: documentData.note,
    fileName: documentData.file.filename,
  });

  if (error instanceof AxiosError) {
    logger.error("Payrix API error details", {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    const status = error.response?.status;
    const payrixData = error.response?.data as Record<string, unknown> | undefined;

    if (status === 400) {
      throw new Error(`Payrix validation error: ${(payrixData?.message as string) || "Invalid request data"}`);
    } else if (status === 401 || status === 403) {
      throw new Error("Payrix authentication failed. Please check API credentials.");
    } else if (status === 404) {
      throw new Error("Note not found. Please ensure the note exists before uploading documents.");
    } else if (status === 413) {
      throw new Error("File too large for Payrix. Please use a smaller file.");
    } else if (status === 422) {
      throw new Error(`Payrix processing error: ${(payrixData?.message as string) || "Unable to process request"}`);
    } else if (status && status >= 500) {
      throw new Error("Payrix service temporarily unavailable. Please try again later.");
    }
  }
}

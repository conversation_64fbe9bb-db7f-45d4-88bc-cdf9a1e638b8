import { createResponse } from "../../../helpers/response.js";
import type { APIGatewayProxyResult } from "aws-lambda";
import type { TokenPaymentValidationResult } from "../validators/token-payment.validator.js";
import type { PaymentProcessingResult } from "../services/token-payment-processor.service.js";

// HTTP Status Codes
const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
} as const;

// Error Messages
const ERROR_MESSAGES = {
  MISSING_REQUIRED_FIELDS: "Missing required fields",
  MISSING_REQUIRED_FIELDS_DETAIL: "merchantId, token, and amount are required",
  INVALID_MERCHANT_ID: "Invalid merchantId",
  INVALID_TOKEN: "Invalid token",
  INVALID_AMOUNT: "Invalid amount",
  VALIDATION_FAILED: "Validation failed",
  MERCHANT_VALIDATION_FAILED: "Merchant validation failed",
  PAYMENT_PROCESSING_FAILED: "Payment processing failed",
  INTERNAL_SERVER_ERROR: "Internal server error",
  DEFAULT_INTERNAL_ERROR: "Failed to process token payment",
  PAYMENT_SUCCESS: "Payment processed successfully",
} as const;

// Field Names for Error Checking
const FIELD_NAMES = {
  MERCHANT_ID: "merchantId",
  TOKEN: "token",
  AMOUNT: "amount",
} as const;

export function createValidationErrorResponse(validationResult: TokenPaymentValidationResult): APIGatewayProxyResult {
  const primaryError = validationResult.errors?.[0] || ERROR_MESSAGES.VALIDATION_FAILED;
  
  if (primaryError.includes(ERROR_MESSAGES.MISSING_REQUIRED_FIELDS)) {
    return createResponse(HTTP_STATUS.BAD_REQUEST, {
      error: ERROR_MESSAGES.MISSING_REQUIRED_FIELDS,
      message: ERROR_MESSAGES.MISSING_REQUIRED_FIELDS_DETAIL,
    });
  }

  if (primaryError.includes(FIELD_NAMES.MERCHANT_ID)) {
    return createResponse(HTTP_STATUS.BAD_REQUEST, {
      error: ERROR_MESSAGES.INVALID_MERCHANT_ID,
      message: primaryError,
    });
  }

  if (primaryError.includes(FIELD_NAMES.TOKEN)) {
    return createResponse(HTTP_STATUS.BAD_REQUEST, {
      error: ERROR_MESSAGES.INVALID_TOKEN,
      message: primaryError,
    });
  }

  if (primaryError.includes(FIELD_NAMES.AMOUNT)) {
    return createResponse(HTTP_STATUS.BAD_REQUEST, {
      error: ERROR_MESSAGES.INVALID_AMOUNT,
      message: primaryError,
    });
  }

  return createResponse(HTTP_STATUS.BAD_REQUEST, {
    error: ERROR_MESSAGES.VALIDATION_FAILED,
    message: primaryError,
    details: validationResult.errors,
  });
}

export function createMerchantValidationErrorResponse(
  merchantId: string,
  error: string
): APIGatewayProxyResult {
  return createResponse(HTTP_STATUS.NOT_FOUND, {
    error: ERROR_MESSAGES.MERCHANT_VALIDATION_FAILED,
    message: error,
    details: {
      merchantId,
      validationError: error,
    },
  });
}

export function createPaymentErrorResponse(
  merchantId: string,
  amount: number,
  error: string
): APIGatewayProxyResult {
  return createResponse(HTTP_STATUS.BAD_REQUEST, {
    error: ERROR_MESSAGES.PAYMENT_PROCESSING_FAILED,
    message: error,
    details: {
      merchantId,
      amount,
    },
  });
}

export function createSuccessResponse(result: PaymentProcessingResult): APIGatewayProxyResult {
  return createResponse(HTTP_STATUS.OK, {
    success: true,
    message: ERROR_MESSAGES.PAYMENT_SUCCESS,
    transaction: result.transaction,
    merchantInfo: result.merchantInfo,
  });
}

export function createInternalErrorResponse(message: string = ERROR_MESSAGES.DEFAULT_INTERNAL_ERROR): APIGatewayProxyResult {
  return createResponse(HTTP_STATUS.INTERNAL_ERROR, {
    error: ERROR_MESSAGES.INTERNAL_SERVER_ERROR,
    message,
  });
}

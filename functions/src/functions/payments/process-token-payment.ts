import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { logger } from "../../helpers/logger.js";
import { validateTokenPaymentRequest, logValidationResult } from "./validators/token-payment.validator.js";
import { processPayment } from "./services/token-payment-processor.service.js";
import {
  createValidationErrorResponse,
  createMerchantValidationErrorResponse,
  createPaymentErrorResponse,
  createSuccessResponse,
  createInternalErrorResponse,
} from "./utils/response-formatter.js";

// Log Messages
const LOG_MESSAGES = {
  PROCESS_TOKEN_REQUEST: "Process token payment request",
  PROCESSING_TOKEN_PAYMENT: "Processing token payment",
  PAYMENT_SUCCESS: "Token payment completed successfully",
  UNEXPECTED_ERROR: "Unexpected error processing token payment",
  UNKNOWN_ERROR: "Unknown error",
} as const;

// Error Patterns
const ERROR_PATTERNS = {
  MERCHANT_VALIDATION_FAILED: "Merchant validation failed",
  INVALID_OR_INACTIVE_MERCHANT: "Invalid or inactive merchant",
} as const;

// Token Display Settings
const TOKEN_DISPLAY = {
  PREFIX_LENGTH: 8,
  SUFFIX: "...",
} as const;

export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  const requestId = event.requestContext.requestId;

  try {
    logger.info(LOG_MESSAGES.PROCESS_TOKEN_REQUEST, {
      requestId,
      hasBody: !!event.body,
    });

    const validationResult = validateTokenPaymentRequest(event.body);
    logValidationResult(validationResult, requestId);

    if (!validationResult.isValid) {
      return createValidationErrorResponse(validationResult);
    }

    const paymentData = validationResult.sanitizedData!;

    logger.info(LOG_MESSAGES.PROCESSING_TOKEN_PAYMENT, {
      requestId,
      merchantId: paymentData.merchantId,
      token: paymentData.token.substring(0, TOKEN_DISPLAY.PREFIX_LENGTH) + TOKEN_DISPLAY.SUFFIX,
      amount: paymentData.amount,
      description: paymentData.description,
    });

    const processingResult = await processPayment(paymentData);

    if (!processingResult.success) {
      if (processingResult.error?.includes(ERROR_PATTERNS.MERCHANT_VALIDATION_FAILED) || processingResult.error?.includes(ERROR_PATTERNS.INVALID_OR_INACTIVE_MERCHANT)) {
        return createMerchantValidationErrorResponse(paymentData.merchantId, processingResult.error);
      }

      return createPaymentErrorResponse(paymentData.merchantId, paymentData.amount, processingResult.error!);
    }

    logger.info(LOG_MESSAGES.PAYMENT_SUCCESS, {
      requestId,
      merchantId: paymentData.merchantId,
      transactionId: processingResult.transaction?.id,
      amount: paymentData.amount,
    });

    return createSuccessResponse(processingResult);
  } catch (error) {
    logger.error(LOG_MESSAGES.UNEXPECTED_ERROR, {
      requestId,
      error: error instanceof Error ? error.message : LOG_MESSAGES.UNKNOWN_ERROR,
      stack: error instanceof Error ? error.stack : undefined,
    });

    return createInternalErrorResponse();
  }
};

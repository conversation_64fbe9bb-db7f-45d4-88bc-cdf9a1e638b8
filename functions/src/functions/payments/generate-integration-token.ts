import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { z } from "zod";
import { createIframeResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { sanitizeInput, withIframeSecurity } from "../../middleware/security.js";
import { validateMerchantById } from "../../service/payrix.service.js";
import { IntegrationTokenRequest } from "../../types/integration-token.types.js";
import { tokenRequestSchema } from "./schemas/integration-token.schema.js";
import {
  generateSecureToken,
  storeToken,
  validateToken as validateTokenService,
  markTokenAsUsed as markTokenAsUsedService,
} from "./services/integration-token.service.js";
import { validateIntegrationTokenRequest } from "./validators/integration-token.validator.js";
import { buildTokenData, buildTokenResponse } from "./utils/token-config-builder.js";

// HTTP Status Codes
const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
} as const;

// Default Values
const DEFAULTS = {
  AMOUNT: 1000,
  EXPIRES_IN_MINUTES: 60,
  CURRENCY: "USD",
  COUNTRY: "USA",
} as const;

// Error Messages
const ERROR_MESSAGES = {
  BODY_REQUIRED: "Request body is required",
  BODY_REQUIRED_DETAIL: "Please provide merchantId and description",
  VALIDATION_FAILED: "Validation failed",
  INVALID_REQUEST: "Invalid request data",
  MERCHANT_VALIDATION_FAILED: "Merchant validation failed",
  INVALID_OR_INACTIVE_MERCHANT: "Invalid or inactive merchant",
  INTERNAL_ERROR: "Internal server error",
  TOKEN_GENERATION_FAILED: "Failed to generate integration token",
  UNKNOWN_ERROR: "Unknown error",
  SUCCESS_MESSAGE: "Integration token generated successfully",
} as const;

// Time Conversion
const TIME_CONVERSION = {
  MINUTES_TO_MS: 60 * 1000,
} as const;

interface MerchantAddress {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
}

interface PayrixMerchant {
  dba?: string;
  name?: string;
  status?: number;
  address?: MerchantAddress;
  email?: string;
  phone?: string;
}

export const validateToken = validateTokenService;
export const markTokenAsUsed = markTokenAsUsedService;

const handlerImpl = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!event.body) {
      return createIframeResponse(HTTP_STATUS.BAD_REQUEST, {
        error: ERROR_MESSAGES.BODY_REQUIRED,
        message: ERROR_MESSAGES.BODY_REQUIRED_DETAIL,
      });
    }

    let requestData: IntegrationTokenRequest;
    try {
      const parsedBody = JSON.parse(event.body);
      const validatedData = tokenRequestSchema.parse(parsedBody);
      requestData = validatedData as IntegrationTokenRequest;
    } catch (error) {
      if (error instanceof z.ZodError) {
        return createIframeResponse(HTTP_STATUS.BAD_REQUEST, {
          error: ERROR_MESSAGES.VALIDATION_FAILED,
          message: ERROR_MESSAGES.INVALID_REQUEST,
          details: error.errors,
        });
      }
      throw error;
    }

    const {
      merchantId,
      description,
      amount = DEFAULTS.AMOUNT,
      returnUrl,
      expiresIn = DEFAULTS.EXPIRES_IN_MINUTES,
      currency = DEFAULTS.CURRENCY,
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
      googlePayConfig,
      enableDigitalWallets,
    } = requestData;

    const validationErrors = validateIntegrationTokenRequest(requestData);
    if (validationErrors.length > 0) {
      const firstError = validationErrors[0];
      return createIframeResponse(firstError.statusCode || HTTP_STATUS.BAD_REQUEST, {
        error: firstError.error,
        message: firstError.message,
        details: firstError.details,
      });
    }

    const sanitizedDescription = sanitizeInput(description);

    const validation = await validateMerchantById(merchantId);

    if (!validation.isValid) {
      return createIframeResponse(HTTP_STATUS.NOT_FOUND, {
        error: ERROR_MESSAGES.MERCHANT_VALIDATION_FAILED,
        message: validation.error || ERROR_MESSAGES.INVALID_OR_INACTIVE_MERCHANT,
        details: {
          merchantId,
          validationError: validation.error,
        },
      });
    }

    const merchant = validation.merchant as PayrixMerchant;

    const token = generateSecureToken();
    const expiresAt = new Date(Date.now() + expiresIn * TIME_CONVERSION.MINUTES_TO_MS);

    const tokenData = buildTokenData(
      merchantId,
      sanitizedDescription,
      amount,
      expiresAt,
      returnUrl,
      {
        currency,
        items,
        taxAmount,
        shippingAmount,
        dutyAmount,
        orderNumber,
        invoiceNumber,
        customerCode,
        orderDiscount,
      },
      merchant?.address || merchant?.email || merchant?.phone
        ? {
            address: merchant?.address
              ? {
                  line1: merchant.address.line1,
                  line2: merchant.address.line2,
                  city: merchant.address.city,
                  state: merchant.address.state,
                  zip: merchant.address.zip,
                  country: merchant.address.country || DEFAULTS.COUNTRY,
                }
              : undefined,
            email: merchant?.email,
            phone: merchant?.phone,
          }
        : undefined,
      {
        googlePayConfig,
        enableDigitalWallets,
      }
    );

    await storeToken(token, tokenData);

    const response = buildTokenResponse(token, expiresAt, {
      id: merchantId,
      name: merchant?.dba || merchant?.name,
      status: merchant?.status,
    });

    return createIframeResponse(HTTP_STATUS.OK, {
      success: true,
      message: ERROR_MESSAGES.SUCCESS_MESSAGE,
      data: response,
    });
  } catch (error) {
    logger.error("Error generating integration token", { error });

    return createIframeResponse(HTTP_STATUS.INTERNAL_ERROR, {
      error: ERROR_MESSAGES.INTERNAL_ERROR,
      message: ERROR_MESSAGES.TOKEN_GENERATION_FAILED,
      details: error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR,
    });
  }
};

export const handler = withIframeSecurity(handlerImpl);

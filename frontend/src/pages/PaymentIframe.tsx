import { usePaymentIframe } from "../hooks/usePaymentIframe";
import { useBillingAddress } from "../hooks/useBillingAddress";
import { usePaymentHandlers } from "../hooks/usePaymentHandlers";

import { TransactionDetails } from "../components/payments/iframe/TransactionDetails";
import { OrderSummary } from "../components/payments/iframe/OrderSummary";
import { BillingAddressForm } from "../components/payments/iframe/BillingAddressForm";
import { PaymentFormSection } from "../components/payments/iframe/PaymentFormSection";
import { PaymentLayout } from "../components/layouts/PaymentLayout";
import { LoadingState } from "../components/payments/iframe/LoadingState";
import { ErrorState } from "../components/payments/iframe/ErrorState";
import { SuccessState } from "../components/payments/iframe/SuccessState";

const PaymentIframe = () => {
  const { payFieldsConfig, merchantInfo, paymentInfo, error: iframeError, loading } = usePaymentIframe();
  const { billingAddress, termsAccepted, handleAddressChange, setTermsAccepted, isAddressValid } = useBillingAddress();
  const { success, error: paymentError, handlePaymentSuccess, handlePaymentFailure } = usePaymentHandlers();

  const error = iframeError || paymentError;

  if (loading) {
    return <LoadingState />;
  }

  if (error && !payFieldsConfig) {
    return <ErrorState error={error} />;
  }

  if (success) {
    return <SuccessState paymentInfo={paymentInfo} />;
  }

  return (
    <PaymentLayout>
      {/* Error Messages */}
      {error && !success && (
        <div className="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border-l-4 border-red-500 animate-fadeIn">
          <p className="flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              ></path>
            </svg>
            {error}
          </p>
        </div>
      )}

      {!success && (
        <div className="flex flex-col h-full">
          {/* Transaction Details */}
          <div className="flex-shrink-0 mb-4">{paymentInfo && <TransactionDetails paymentInfo={paymentInfo} />}</div>

          {/* Main Form Section with 3 columns */}
          <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:items-start">
            {/* Transaction Summary Column */}
            <div className="order-1 lg:order-1 lg:col-span-1 flex flex-col h-full">
              <div className="flex-1">
                <OrderSummary paymentInfo={paymentInfo} merchantInfo={merchantInfo} />
              </div>
            </div>

            {/* Address Form Column */}
            <div className="order-2 lg:order-2 lg:col-span-1 flex flex-col h-full">
              <div className="flex-1">
                <BillingAddressForm
                  billingAddress={billingAddress}
                  termsAccepted={termsAccepted}
                  handleAddressChange={handleAddressChange}
                  setTermsAccepted={setTermsAccepted}
                  error={error}
                  isAddressValid={isAddressValid()}
                />
              </div>
            </div>

            {/* Payment Form Column */}
            <div className="order-3 lg:order-3 lg:col-span-1 flex flex-col h-full">
              <div className="flex-1">
                <PaymentFormSection
                  payFieldsConfig={payFieldsConfig}
                  paymentInfo={paymentInfo}
                  billingAddress={billingAddress}
                  error={error}
                  isAddressValid={isAddressValid()}
                  loading={loading}
                  onSuccess={handlePaymentSuccess}
                  onFailure={handlePaymentFailure}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </PaymentLayout>
  );
};

export default PaymentIframe;

import { useDispatch } from "react-redux";
import { useState } from "react";
import { toast } from "sonner";
import { updateFormData, nextStep } from "../../../redux/slices/onboardingSlice";
import { Member, DEFAULT_ADDITIONAL_MEMBER } from "../constants/ownerConstants";
import { validateOwnerForm } from "../utils/ownerValidation";
import { ensureSinglePrimaryContact } from "../utils/ownershipUtils";

interface FormData {
  merchant?: {
    dba?: string;
    new?: number;
    mcc?: string;
    status?: string;
    members?: Member[];
  };
  username?: string;
  password?: string;
  confirmPassword?: string;
}

export const useOwnerFormHandlers = (formData: FormData, members: Member[]) => {
  const dispatch = useDispatch();
  // errors state to track validation errors for form fields
  const [errors, setErrors] = useState<Record<string, string>>({});

  // validateForm to validate owner information and credentials
  const validateForm = () => {
    const validationErrors = validateOwnerForm(members, formData.username, formData.password, formData.confirmPassword);
    setErrors(validationErrors);
    return validationErrors;
  };

  // handleSubmit to validate form and proceed to next step
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length === 0) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(validationErrors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);

      // scroll to first error field for better UX
      setTimeout(() => {
        const firstErrorKey = Object.keys(validationErrors)[0];
        if (firstErrorKey) {
          const errorElement = document.querySelector(`[data-error-key="${firstErrorKey}"]`);
          if (errorElement) {
            errorElement.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      }, 100);
    }
  };

  // handleChange to update member field values and clear related errors
  const handleChange = (memberIndex: number, field: string, value: string | number) => {
    // clear error for this field when user starts typing
    const errorKey = `member${memberIndex}.${field}`;
    if (errors[errorKey]) {
      const newErrors = { ...errors };
      delete newErrors[errorKey];
      setErrors(newErrors);
    }

    // update the specific member's field value
    const updatedMembers = [...members];
    updatedMembers[memberIndex] = {
      ...updatedMembers[memberIndex],
      [field]: value,
    };

    dispatch(
      updateFormData({
        merchant: {
          dba: "",
          new: formData.merchant?.new ?? 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  // addOwner to add a new owner member to the form
  const addOwner = () => {
    const newMember = { ...DEFAULT_ADDITIONAL_MEMBER, primary: "0" };
    const updatedMembers = ensureSinglePrimaryContact([...members, newMember]);
    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: formData.merchant?.new ?? 1,
          mcc: formData.merchant?.mcc || "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  // handlePrimaryChange to set a new primary contact
  const handlePrimaryChange = (selectedIndex: number) => {
    const updatedMembers = ensureSinglePrimaryContact(members, selectedIndex);
    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: formData.merchant?.new ?? 1,
          mcc: formData.merchant?.mcc || "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  // removeOwner to remove an owner member from the form
  const removeOwner = (index: number) => {
    if (members.length > 1) {
      // check if the removed member was the primary contact
      const wasRemovedMemberPrimary = members[index].primary === "1";
      const filteredMembers = members.filter((_, i) => i !== index);

      // if primary contact was removed, make the first remaining member primary
      const updatedMembers = wasRemovedMemberPrimary ? ensureSinglePrimaryContact(filteredMembers, 0) : ensureSinglePrimaryContact(filteredMembers);

      dispatch(
        updateFormData({
          merchant: {
            dba: formData.merchant?.dba || "",
            new: formData.merchant?.new ?? 1,
            mcc: formData.merchant?.mcc || "",
            status: "1",
            ...formData.merchant,
            members: updatedMembers,
          },
        })
      );
    }
  };

  return {
    errors,
    setErrors,
    validateForm,
    handleSubmit,
    handleChange,
    addOwner,
    handlePrimaryChange,
    removeOwner,
  };
};

import { useMemo, useRef, useState } from "react";
import { usePayFields } from "./hooks/usePayFields";
import { SecurePayFieldsProps } from "./types/payfields.types";
import { Wallet, CreditCard, Loader2 } from "lucide-react";

const SecurePayFields = ({ config, paymentInfo, onSuccess, onFailure, className = "", billingAddress }: SecurePayFieldsProps) => {
  const cardNumberRef = useRef<HTMLDivElement>(null);
  const cardNameRef = useRef<HTMLDivElement>(null);
  const cardCvvRef = useRef<HTMLDivElement>(null);
  const cardExpirationRef = useRef<HTMLDivElement>(null);

  const googlePayAvailable = !!config?.googlePayConfig?.enabled;

  // none | card | googlePay
  const [selectedMethod, setSelectedMethod] = useState<"none" | "card" | "googlePay">(googlePayAvailable ? "none" : "card");

  // Ensure PayFields initializes correctly depending on visible method while keeping DOM nodes mounted
  const effectiveConfig = useMemo(() => {
    if (!config) return config;
    if (selectedMethod === "card") {
      return {
        ...config,
        googlePayConfig: config.googlePayConfig ? { ...config.googlePayConfig, enabled: false } : undefined,
      };
    }
    if (selectedMethod === "googlePay") {
      // Keep Google Pay enabled; rest unchanged
      return {
        ...config,
        googlePayConfig: config.googlePayConfig ? { ...config.googlePayConfig, enabled: true } : undefined,
      };
    }
    // selection screen: keep original config so both are ready; UI hides both until a selection
    return config;
  }, [config, selectedMethod]);

  const { scriptError, isSubmitting, validationError, isLoading, handleSubmit } = usePayFields({
    config: effectiveConfig,
    paymentInfo,
    billingAddress,
    onSuccess,
    onFailure,
  });

  if (scriptError) {
    return (
      <div className={`p-4 bg-red-50 text-red-800 rounded-md ${className}`}>
        <p>{scriptError}</p>
      </div>
    );
  }

  if (!config) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading secure payment form...</p>
      </div>
    );
  }

  const LoadingOverlay = () => (
    <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10 rounded-lg">
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-12 h-12 mb-4 bg-blue-100 rounded-full">
          <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
        </div>
        <p className="text-sm font-medium text-gray-900 mb-1">Loading secure payment form...</p>
        <p className="text-xs text-gray-500">Please wait while we prepare your payment options</p>
      </div>
    </div>
  );

  const renderSelectionScreen = () => {
    if (selectedMethod !== "none") return null;

    return (
      <div className="space-y-3">
        <p className="text-sm text-gray-600">Choose a payment method</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {/* Pay with Card */}
          <button
            type="button"
            onClick={() => setSelectedMethod("card")}
            className="p-3 sm:p-4 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 hover:border-gray-300 transition-colors text-left"
          >
            <div className="flex items-center gap-3">
              <span className="inline-flex items-center justify-center h-9 w-9 rounded-md bg-gray-100 text-gray-700">
                <CreditCard className="h-5 w-5" />
              </span>
              <div>
                <p className="text-sm font-medium text-gray-900">Card</p>
                <p className="text-xs text-gray-500">Enter card details securely</p>
              </div>
            </div>
          </button>

          {/* Google Pay */}
          {googlePayAvailable && (
            <button
              type="button"
              onClick={() => setSelectedMethod("googlePay")}
              className="p-3 sm:p-4 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 hover:border-gray-300 transition-colors text-left"
            >
              <div className="flex items-center gap-3">
                <span className="inline-flex items-center justify-center h-9 w-9 rounded-md bg-gray-100 text-gray-700">
                  <Wallet className="h-5 w-5" />
                </span>
                <div>
                  <p className="text-sm font-medium text-gray-900">Google Pay</p>
                  <p className="text-xs text-gray-500">Fast checkout with Google</p>
                </div>
              </div>
            </button>
          )}
        </div>
      </div>
    );
  };

  const renderToggle = () => {
    if (selectedMethod === "none") return null;

    const canToggleToGooglePay = googlePayAvailable && selectedMethod === "card";
    const canToggleToCard = selectedMethod === "googlePay";

    const label = canToggleToGooglePay ? "Switch to Google Pay" : canToggleToCard ? "Switch to Card Payment" : "Change payment method";

    const onToggle = () => {
      if (canToggleToGooglePay) setSelectedMethod("googlePay");
      else if (canToggleToCard) setSelectedMethod("card");
    };

    return (
      <div className="flex items-center justify-between mb-4">
        {(canToggleToGooglePay || canToggleToCard) && (
          <button type="button" onClick={onToggle} className="text-sm text-[#364F6B] hover:text-[#2A3F59] underline cursor-pointer">
            {label}
          </button>
        )}
      </div>
    );
  };

  return (
    <div className={`secure-payfields relative ${className}`}>
      {validationError && (
        <div className="p-4 mb-4 bg-red-50 text-red-800 rounded-md">
          <p>{validationError}</p>
        </div>
      )}

      {/* Loading overlay */}
      {isLoading && <LoadingOverlay />}

      {/* Selection screen */}
      {renderSelectionScreen()}

      {/* Selected method header + toggle */}
      {renderToggle()}

      {/* Google Pay Section */}
      {config.googlePayConfig?.enabled && (
        <div className={`${selectedMethod === "googlePay" ? "block" : "hidden"} space-y-4`}>
          {/* Google Pay button container with better styling */}
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <p className="text-sm text-gray-600 mb-4">Fast and secure checkout with Google Pay</p>
            <div id="googlePayButton" className="flex justify-center"></div>

            {/* Security badges */}
            <div className="mt-6 flex items-center justify-center gap-4 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
                <span>Secure Payment</span>
              </div>
              <div className="flex items-center gap-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
                <span>Protected by Google</span>
              </div>
            </div>
          </div>

          {/* Info message */}
          <p className="text-xs text-center text-gray-500 mt-4">By continuing, you agree to the payment terms and conditions</p>
        </div>
      )}

      {/* Card Payment Section (always mounted, visibility controlled) */}
      <div className={`${selectedMethod === "card" ? "block" : "hidden"} space-y-4`}>
        <h3 className="text-lg font-medium text-gray-900 mb-3">Card Information</h3>

        <div>
          <label htmlFor="card-number" className="block mb-2 text-sm font-medium text-gray-700">
            Card Number
          </label>
          <div id="card-number" ref={cardNumberRef} className="h-12 border rounded-md"></div>
        </div>

        <div>
          <label htmlFor="card-name" className="block mb-2 text-sm font-medium text-gray-700">
            Cardholder Name
          </label>
          <div id="card-name" ref={cardNameRef} className="h-12 border rounded-md"></div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="card-expiration" className="block mb-2 text-sm font-medium text-gray-700">
              Expiration Date
            </label>
            <div id="card-expiration" ref={cardExpirationRef} className="h-12 border rounded-md"></div>
          </div>
          <div>
            <label htmlFor="card-cvv" className="block mb-2 text-sm font-medium text-gray-700">
              CVV
            </label>
            <div id="card-cvv" ref={cardCvvRef} className="h-12 border rounded-md"></div>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className={`w-full py-3 px-4 font-medium rounded-md transition-all ${
            isSubmitting ? "bg-gray-400 cursor-not-allowed text-white" : "bg-[#364F6B] hover:bg-[#2A3F59] text-white shadow-md hover:shadow-lg"
          }`}
        >
          {isSubmitting ? (
            <span className="flex items-center justify-center">
              <Loader2 className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
              Processing...
            </span>
          ) : (
            `Pay $${((paymentInfo?.amount || config.amount) / 100).toFixed(2)} ${paymentInfo?.currency || "USD"}`
          )}
        </button>

        {/* Save card option (card only) */}
        <div className="mt-4">
          <label className="flex items-center text-sm text-gray-600">
            <input type="checkbox" className="mr-2 h-4 w-4 text-[#364F6B] border-gray-300 rounded focus:ring-[#364F6B]" />
            Save card for future payments
          </label>
        </div>
      </div>
    </div>
  );
};

export default SecurePayFields;

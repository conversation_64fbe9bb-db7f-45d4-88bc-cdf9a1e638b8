import { toast } from "sonner";
import { processTokenPayment } from "../../../services/api";
import { PayFieldsConfig, BillingAddress, PaymentResponse, PaymentError } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import { postMessageToParent } from "../utils/iframe-communication";

// Payment Messages
const MESSAGES = {
  SUCCESS: "Payment processed successfully!",
  RESPONSE_NULL: "Payment response is null or undefined",
  TOKEN_GENERATION_FAILED: "Token generation failed: No token received from PayFields",
  TOKEN_PROCESSING_FAILED: "Token payment processing failed",
  PAYMENT_FAILED_PREFIX: "Payment failed: ",
  PAYMENT_PROCESSING_FAILED: "Payment processing failed. Please try again.",
  PAYMENT_VALIDATION_FAILED: "Payment validation failed. Please check your card details.",
  VALIDATION_CHECK_DETAILS: "Please check your card details",
  USER_CANCELLED: "Payment cancelled by user",
} as const;

// Event Types  
const PAYMENT_EVENTS = {
  SUCCESS: "PAYMENT_SUCCESS",
  ERROR: "PAYMENT_ERROR",
  CANCELLED: "PAYMENT_CANCELLED",
  FAILURE: "PAYMENT_FAILURE",
  VALIDATION_FAILURE: "PAYMENT_VALIDATION_FAILURE",
  FINISHED: "PAYMENT_FINISHED",
} as const;

// Payment Modes
const PAYMENT_MODES = {
  TOKEN: "token",
} as const;

// Cancellation Status Codes
const CANCELLATION_CODES = {
  CANCELED: "CANCELED",
  CANCELLED: "CANCELLED",
} as const;

// Cancellation Keywords
const CANCELLATION_KEYWORDS = [
  "canceled",
  "cancelled",
  "user canceled",
  "user cancelled",
  "user_canceled",
  "user_cancelled",
  "aborted",
  "dismissed",
  "closed",
  "user closed",
  "user dismissed",
  "payment_canceled",
  "payment_cancelled",
  "google pay canceled",
  "google pay cancelled",
] as const;

// Helper function to extract token information from various response formats
const extractTokenFromResponse = (response: PaymentResponse): { token?: string; tokenId?: string } => {
  // Check array format (typical PayFields response)
  if (response.data && Array.isArray(response.data) && response.data.length > 0) {
    const firstDataItem = response.data[0];
    return {
      token: firstDataItem?.token,
      tokenId: firstDataItem?.id,
    };
  }
  
  // Check direct token format
  if (response.token) {
    return {
      token: response.token,
      tokenId: response.id || response.token,
    };
  }
  
  // Check details format (alternative response structure)
  if (response.details) {
    return {
      token: response.details.token,
      tokenId: response.details.id || response.details.token,
    };
  }
  
  return {};
};

// Helper function to build customer info from billing address
const buildCustomerInfo = (billingAddress?: BillingAddress) => {
  if (!billingAddress) return undefined;
  
  return {
    name: `${billingAddress.firstName} ${billingAddress.lastName}`,
    email: billingAddress.email,
    address: {
      line1: billingAddress.line1,
      line2: billingAddress.line2,
      city: billingAddress.city,
      state: billingAddress.state,
      zip: billingAddress.zip,
      country: billingAddress.country,
    },
  };
};

export const createPaymentSuccessHandler = (
  config: PayFieldsConfig,
  paymentInfo: PaymentInfo | null,
  billingAddress?: BillingAddress,
  onSuccess?: (response: unknown) => void
) => {
  return async (response: PaymentResponse) => {
    if (!response) {
      throw new Error(MESSAGES.RESPONSE_NULL);
    }

    const isTokenMode = config.mode === PAYMENT_MODES.TOKEN;
    
    if (!isTokenMode) {
      // Non-token mode: direct payment processing
      toast.success(MESSAGES.SUCCESS);
      postMessageToParent(PAYMENT_EVENTS.SUCCESS, { data: response });
      if (onSuccess) onSuccess(response);
      return;
    }

    // Token mode: extract token and process payment
    const { token: extractedToken, tokenId: extractedTokenId } = extractTokenFromResponse(response);

    if (!extractedToken) {
      // Token generation failed
      const errorMessage = MESSAGES.TOKEN_GENERATION_FAILED;
      toast.error(errorMessage);
      postMessageToParent(PAYMENT_EVENTS.ERROR, {
        error: errorMessage,
        tokenGenerationFailed: true,
      });

      if (onSuccess) {
        onSuccess({
          error: errorMessage,
          tokenGenerationFailed: true,
        });
      }
      throw new Error(errorMessage);
    }

    // Process the payment with the extracted token
    const actualAmount = paymentInfo?.amount || config.amount;
    const paymentDescription = paymentInfo?.description || config.description;

    try {
      const paymentResult = await processTokenPayment({
        merchantId: config.merchantId,
        token: extractedToken,
        tokenId: extractedTokenId || extractedToken,
        amount: actualAmount,
        description: paymentDescription,
        customerInfo: buildCustomerInfo(billingAddress),
      });

      if (!paymentResult.success) {
        throw new Error(paymentResult.message || MESSAGES.TOKEN_PROCESSING_FAILED);
      }

      // Payment successful
      toast.success(MESSAGES.SUCCESS);

      const successResponse = {
        ...response,
        transaction: paymentResult.transaction,
        merchantInfo: paymentResult.merchantInfo,
        tokenProcessed: true,
      };

      postMessageToParent(PAYMENT_EVENTS.SUCCESS, { data: successResponse });
      if (onSuccess) onSuccess(successResponse);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : MESSAGES.TOKEN_PROCESSING_FAILED;
      toast.error(`${MESSAGES.PAYMENT_FAILED_PREFIX}${errorMessage}`);

      const errorResponse = {
        error: errorMessage,
        tokenGenerated: true,
        tokenProcessingFailed: true,
      };

      postMessageToParent(PAYMENT_EVENTS.ERROR, errorResponse);
      if (onSuccess) onSuccess(errorResponse);
      throw error;
    }
  };
};

const isGooglePayCancellation = (err: PaymentError): boolean => {
  if (!err) return false;

  // Check for statusCode property indicating cancellation (Google Pay specific)
  if (err.statusCode === CANCELLATION_CODES.CANCELED || err.statusCode === CANCELLATION_CODES.CANCELLED) {
    return true;
  }

  // Check message-based cancellation patterns
  if (!err.message) return false;

  const message = err.message.toLowerCase();
  return CANCELLATION_KEYWORDS.some((keyword) => message.includes(keyword));
};

export const createPaymentFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: PaymentError) => {
    // Check if this is a Google Pay user cancellation
    if (isGooglePayCancellation(err)) {
      // Don't show error toast for user cancellations
      postMessageToParent(PAYMENT_EVENTS.CANCELLED, {
        message: MESSAGES.USER_CANCELLED,
        details: err,
      });

      // Don't call onFailure for user cancellations to avoid error states
      return;
    }

    // Handle actual payment errors
    let errorMessage: string = MESSAGES.PAYMENT_PROCESSING_FAILED;
    if (err && Array.isArray(err.errors)) {
      const fieldErrors = err.errors.map((e) => `${e.field}: ${e.msg}`).join(", ");
      errorMessage = `Payment failed: ${fieldErrors}`;
    } else if (err && err.message) {
      errorMessage = err.message;
    }

    toast.error(errorMessage);
    postMessageToParent(PAYMENT_EVENTS.FAILURE, {
      error: errorMessage,
      details: err,
    });

    if (onFailure) onFailure(err);
  };
};

export const createValidationFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: unknown) => {
    const validationMessage = MESSAGES.PAYMENT_VALIDATION_FAILED;
    toast.error(MESSAGES.VALIDATION_CHECK_DETAILS);

    postMessageToParent(PAYMENT_EVENTS.VALIDATION_FAILURE, {
      error: validationMessage,
      details: err,
    });

    if (onFailure) onFailure({ message: validationMessage, details: err });
  };
};

export const createPaymentFinishHandler = () => {
  return (response: unknown) => {
    postMessageToParent(PAYMENT_EVENTS.FINISHED, { data: response });
  };
};

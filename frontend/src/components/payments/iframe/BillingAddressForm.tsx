import type { BillingAddress } from "../../../types/payment";
import { AddressFields } from "./AddressFields";
import { PrimaryColor } from "../../../constants/colors";
import { PaymentValidation } from "./PaymentValidation";

interface BillingAddressFormProps {
  billingAddress: BillingAddress;
  termsAccepted: boolean;
  handleAddressChange: (field: keyof BillingAddress, value: string) => void;
  setTermsAccepted: (accepted: boolean) => void;
  error: string | null;
  isAddressValid: boolean;
}

export const BillingAddressForm = ({
  billingAddress,
  termsAccepted,
  handleAddressChange,
  setTermsAccepted,
  error,
  isAddressValid,
}: BillingAddressFormProps) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 shadow-sm h-full flex flex-col">
      <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-3" style={{ color: PrimaryColor.hex }}>
        Billing Address
      </h3>

      <AddressFields billingAddress={billingAddress} handleAddressChange={handleAddressChange} />

      {/* Compact Terms and Conditions */}
      <div className="pt-3 border-t border-gray-100">
        <div className="flex items-start space-x-2 min-h-[44px]">
          <input
            type="checkbox"
            id="terms"
            checked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
            className="mt-0.5 h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            aria-describedby="terms-description"
          />
          <label htmlFor="terms" className="flex-1 py-2 cursor-pointer text-xs text-gray-700 leading-relaxed" id="terms-description">
            I agree to the{" "}
            <button
              type="button"
              className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
              onClick={() => {}}
            >
              terms
            </button>
            {", "}
            <button
              type="button"
              className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
              onClick={() => {}}
            >
              privacy policy
            </button>
            {", and "}
            <button
              type="button"
              className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
              onClick={() => {}}
            >
              refund policy
            </button>
            .
          </label>
        </div>
      </div>

      <div className="mb-4">
        <PaymentValidation error={error} isAddressValid={isAddressValid} />
      </div>

      {/* <SecurityInfoSection /> */}
    </div>
  );
};

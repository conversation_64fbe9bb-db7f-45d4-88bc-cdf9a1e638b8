import { PrimaryColor } from "../../../constants/colors";
import type { BillingAddress, PayFieldsConfig, PaymentInfo } from "../../../types/payment";
import SecurePayFields from "../SecurePayFields";
import { CardBrandLogos } from "./CardBrandLogos";

interface PaymentFormSectionProps {
  payFieldsConfig: PayFieldsConfig | null;
  paymentInfo: PaymentInfo | null;
  billingAddress: BillingAddress;
  error: string | null;
  isAddressValid: boolean;
  loading?: boolean;
  onSuccess: (response: unknown) => void;
  onFailure: (error: unknown) => void;
}

export const PaymentFormSection = ({
  payFieldsConfig,
  paymentInfo,
  billingAddress,
  isAddressValid,
  loading = false,
  onSuccess,
  onFailure,
}: PaymentFormSectionProps) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 sm:p-5 shadow-sm h-full flex flex-col">
      <div className="flex items-center gap-2 mb-2 sm:mb-3">
        <h3 className="text-base sm:text-lg font-medium" style={{ color: PrimaryColor.hex }}>
          Payment Information
        </h3>
      </div>

      {/* Accepted cards */}
      <div className="mb-4">
        <CardBrandLogos showGooglePay={payFieldsConfig?.googlePayConfig?.enabled || false} />
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin h-8 w-8 border-4 border-t-transparent border-blue-600 rounded-full mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading payment methods...</p>
        </div>
      ) : payFieldsConfig ? (
        <div className={!isAddressValid ? "opacity-50 pointer-events-none" : ""}>
          <SecurePayFields
            config={payFieldsConfig}
            paymentInfo={paymentInfo}
            onSuccess={onSuccess}
            onFailure={onFailure}
            billingAddress={billingAddress}
          />
        </div>
      ) : null}
    </div>
  );
};

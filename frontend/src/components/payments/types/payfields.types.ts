export interface GooglePayConfig {
  enabled?: boolean;
  merchantName?: string;
  environment?: "TEST" | "PRODUCTION";
  allowedCardNetworks?: Array<"VISA" | "MASTERCARD" | "AMEX" | "DISCOVER" | "JCB" | "INTERAC">;
  allowedCardAuthMethods?: Array<"PAN_ONLY" | "CRYPTOGRAM_3DS">;
  billingAddressRequired?: boolean;
  shippingAddressRequired?: boolean;
  phoneNumberRequired?: boolean;
}

export interface PayFieldsWindow {
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
  onValidationFailure?: (error: unknown) => void;
  onFinish?: (response: unknown) => void;
  init: (config: unknown) => void;
  submit: () => void;
  ready: () => void;
  unmountAll?: () => void;
  config: {
    apiKey: string;
    merchant: string;
    mode: string;
    txnType: string;
    amount: number | string; // Support both number and string for token mode
    description: string;
    name?: string;
    placeholders?: Record<string, string>;
    iframe?: boolean;
    responsive?: boolean;
    autoResize?: boolean;
    billing?: {
      name?: string;
      email?: string;
      phone?: string;
      address?: string;
      address2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    googlePay?: {
      enabled: boolean;
      environment: "TEST" | "PRODUCTION";
    };
  };
  fields?: unknown[];
  customizations: {
    style?: Record<string, Record<string, string>>;
    placeholders?: Record<string, string>;
  };
}

export interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
  googlePayConfig?: GooglePayConfig;
  enableDigitalWallets?: boolean;
}

export interface BillingAddress {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

export interface SecurePayFieldsProps {
  config: PayFieldsConfig;
  paymentInfo?: import("../../../types/payment").PaymentInfo | null;
  onSuccess?: (response: unknown) => void;
  onFailure?: (error: unknown) => void;
  className?: string;
  billingAddress?: BillingAddress;
}

export interface PaymentResponse {
  token?: string;
  id?: string;
  transaction?: unknown;
  merchantInfo?: unknown;
  // PayFields actual response structure
  data?: Array<{
    token?: string;
    id?: string;
    [key: string]: unknown;
  }>;
  details?: {
    token?: string;
    id?: string;
    [key: string]: unknown;
  };
  errors?: Array<unknown>;
}

export interface PaymentError {
  message?: string;
  statusCode?: string;
  details?: unknown;
  errors?: Array<{ field: string; msg: string }>;
}

export type PaymentMessageType =
  | "PAYMENT_SUCCESS"
  | "PAYMENT_ERROR"
  | "PAYMENT_FAILURE"
  | "PAYMENT_CANCELLED"
  | "PAYMENT_VALIDATION_FAILURE"
  | "PAYMENT_FINISHED"
  | "PAYMENT_SUBMISSION_STARTED"
  | "PAYMENT_TIMEOUT"
  | "PAYMENT_SUBMISSION_ERROR"
  | "PAYFIELDS_SCRIPT_LOADED"
  | "PAYFIELDS_SCRIPT_ERROR";

export interface PaymentMessage {
  type: PaymentMessageType;
  data?: unknown;
  error?: string;
  message?: string;
  details?: unknown;
  timestamp: string;
  tokenGenerated?: boolean;
  tokenProcessingFailed?: boolean;
  tokenGenerationFailed?: boolean;
  tokenProcessed?: boolean;
}
